import { Conversation, ChatMessage } from '../utils/chatService';
import indexedDBService from './indexedDBService';

// Flag to track if initial setup has been completed
let isInitialized = false;
let initPromise: Promise<void> | null = null;

/**
 * Initialize the conversation service and database
 */
export const initializeService = (): Promise<void> => {
  if (isInitialized) {
    return Promise.resolve();
  }

  if (initPromise) {
    return initPromise;
  }

  initPromise = indexedDBService.init().then(() => {
    console.log('Conversation service initialized');
    isInitialized = true;
  }).catch(error => {
    console.error('Failed to initialize conversation service:', error);
    initPromise = null;
    throw error;
  });

  return initPromise;
};

/**
 * Helper to ensure service is initialized before operations
 */
const ensureInitialized = async (): Promise<void> => {
  if (!isInitialized && !initPromise) {
    await initializeService();
  } else if (initPromise) {
    await initPromise;
  }
};

/**
 * Loads all conversations from IndexedDB.
 * Returns an empty array if no conversations exist.
 */
export const loadConversations = async (): Promise<Conversation[]> => {
  console.log('loadConversations called');
  await ensureInitialized();
  try {
    console.log('Getting all conversations from IndexedDB');
    const conversations = await indexedDBService.getAllConversations();
    console.log(`Retrieved ${conversations.length} conversations from IndexedDB`);

    // Sort by lastModified descending (newest first)
    const sortedConversations = conversations.sort((a, b) =>
      (b.lastModified || 0) - (a.lastModified || 0)
    );

    if (sortedConversations.length > 0) {
      console.log('Most recent conversation:', sortedConversations[0].id,
        'Title:', sortedConversations[0].title,
        'Last modified:', new Date(sortedConversations[0].lastModified || 0).toISOString());
    }

    return sortedConversations;
  } catch (error) {
    console.error('Error loading conversations:', error);
    return []; // In case of error, return empty array to prevent app crash
  }
};

/**
 * Gets a single conversation by ID
 */
export const getConversation = async (id: string): Promise<Conversation | null> => {
  await ensureInitialized();
  try {
    return await indexedDBService.getConversation(id);
  } catch (error) {
    console.error(`Error getting conversation ${id}:`, error);
    return null;
  }
};

/**
 * Wait for a conversation to become available
 * Useful when dealing with race conditions
 */
export const waitForConversation = async (
  conversationId: string,
  maxRetries = 5,
  delayMs = 100
): Promise<Conversation | null> => {
  await ensureInitialized();
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    const conversation = await getConversation(conversationId);
    if (conversation) {
      return conversation;
    }
    // Wait before retrying
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }
  console.error(`Conversation ${conversationId} not found after ${maxRetries} attempts`);
  return null;
};

/**
 * Adds a message to an existing conversation
 * Includes retry logic if the conversation isn't found initially
 */
export const addMessageToConversation = async (
  conversationId: string,
  message: ChatMessage,
  maxRetries = 3
): Promise<Conversation | null> => {
  await ensureInitialized();
  let conversation = await getConversation(conversationId);

  if (!conversation) {
    // If conversation not found and we still have retries left
    if (maxRetries > 0) {
      console.log(`Conversation with id ${conversationId} not found. Retrying... (${maxRetries} retries left)`);
      // Wait for a moment to allow any in-progress operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));
      return addMessageToConversation(conversationId, message, maxRetries - 1);
    }
    console.error(`Conversation with id ${conversationId} not found after retries.`);
    return null;
  }

  // Add message to conversation
  conversation.messages.push(message);
  conversation.lastModified = Date.now();

  // Update the conversation in IndexedDB
  await indexedDBService.updateConversation(conversation);

  return conversation;
};

/**
 * Creates a new conversation with an initial message
 * Uses the initialMessageProcessed flag to prevent duplicate creation
 */
export const createConversation = async (title?: string): Promise<Conversation> => {
  await ensureInitialized();
  const conversation: Conversation = {
    id: crypto.randomUUID(),
    messages: [],
    title: title || 'New Chat',
    createdAt: Date.now(),
    lastModified: Date.now()
  };

  await indexedDBService.addConversation(conversation);
  return conversation;
};

/**
 * Deletes a conversation
 */
export const deleteConversation = async (id: string): Promise<void> => {
  await ensureInitialized();
  try {
    await indexedDBService.deleteConversation(id);
  } catch (error) {
    console.error(`Error deleting conversation ${id}:`, error);
  }
};

/**
 * Updates a conversation's title
 */
export const updateConversationTitle = async (id: string, newTitle: string): Promise<Conversation | null> => {
  await ensureInitialized();
  const conversation = await indexedDBService.getConversation(id);

  if (!conversation) {
    console.error(`Conversation with id ${id} not found for title update.`);
    return null;
  }

  // Update title and lastModified timestamp
  conversation.title = newTitle;
  conversation.lastModified = Date.now();

  // Save to IndexedDB
  await indexedDBService.updateConversation(conversation);

  return conversation;
};

/**
 * Updates a conversation with a new message while maintaining user-selected message history limit
 */
export const updateConversation = async (conversationId: string, message: ChatMessage): Promise<Conversation | null> => {
  await ensureInitialized();
  try {
    const conversation = await getConversation(conversationId);
    if (!conversation) return null;

    // Get user's selected message memory limit from localStorage
    const userSelectedMemory = parseInt(localStorage.getItem('chat_memory_limit') || '5', 10);
    const maxMessages = Math.min(Math.max(1, userSelectedMemory), 30); // Ensure between 1 and 30

    // Add new message and keep only the last N messages based on user's selection
    conversation.messages = [...conversation.messages, message].slice(-maxMessages);
    conversation.lastModified = Date.now();

    await indexedDBService.updateConversation(conversation);
    return conversation;
  } catch (error) {
    console.error('Error updating conversation:', error);
    return null;
  }
};